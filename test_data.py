#!/usr/bin/env python3
"""
Test skript pro ověření stažených dat a ukázku použití
"""

import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import yaml


def load_config():
    """Načte konfiguraci"""
    with open('config.yaml', 'r', encoding='utf-8') as file:
        return yaml.safe_load(file)


def test_data_integrity():
    """Test integrity stažených dat"""
    print("🔍 Test integrity dat")
    print("=" * 30)
    
    config = load_config()
    base_dir = Path(config['data']['base_directory'])
    
    timeframe_mapping = {
        "1m": "M1", "5m": "M5", "15m": "M15", "30m": "M30",
        "1h": "H1", "4h": "H4", "1d": "D1"
    }
    
    issues = []
    
    for timeframe in config['data']['timeframes']:
        folder_name = timeframe_mapping.get(timeframe, timeframe.upper())
        folder_path = base_dir / folder_name
        
        if not folder_path.exists():
            issues.append(f"❌ Složka {folder_name} neexistuje")
            continue
            
        print(f"\n📁 Testování {folder_name}:")
        
        parquet_files = list(folder_path.glob("*.parquet"))
        
        if not parquet_files:
            issues.append(f"❌ Žádné soubory v {folder_name}")
            continue
            
        for file_path in parquet_files:
            try:
                df = pd.read_parquet(file_path)
                
                # Test základních sloupců
                required_cols = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
                missing_cols = [col for col in required_cols if col not in df.columns]
                
                if missing_cols:
                    issues.append(f"❌ {file_path.name}: Chybí sloupce {missing_cols}")
                    continue
                
                # Test datových typů
                if df['timestamp'].dtype not in ['datetime64[ns]', 'object']:
                    issues.append(f"⚠️  {file_path.name}: Nesprávný typ timestamp")
                
                # Test OHLC logiky
                invalid_ohlc = df[
                    (df['high'] < df['low']) | 
                    (df['high'] < df['open']) | 
                    (df['high'] < df['close']) |
                    (df['low'] > df['open']) | 
                    (df['low'] > df['close'])
                ]
                
                if len(invalid_ohlc) > 0:
                    issues.append(f"⚠️  {file_path.name}: {len(invalid_ohlc)} neplatných OHLC záznamů")
                
                # Test duplicitních timestampů
                duplicates = df['timestamp'].duplicated().sum()
                if duplicates > 0:
                    issues.append(f"⚠️  {file_path.name}: {duplicates} duplicitních timestampů")
                
                # Test nulových hodnot
                null_counts = df[required_cols].isnull().sum()
                if null_counts.sum() > 0:
                    issues.append(f"⚠️  {file_path.name}: Nulové hodnoty - {null_counts.to_dict()}")
                
                print(f"  ✅ {file_path.name}: {len(df):,} záznamů")
                
            except Exception as e:
                issues.append(f"❌ {file_path.name}: Chyba při čtení - {e}")
    
    print(f"\n📊 Shrnutí:")
    if issues:
        print(f"❌ Nalezeno {len(issues)} problémů:")
        for issue in issues:
            print(f"  {issue}")
    else:
        print("✅ Všechna data jsou v pořádku!")
    
    return len(issues) == 0


def analyze_data_coverage():
    """Analýza pokrytí dat"""
    print("\n📈 Analýza pokrytí dat")
    print("=" * 30)
    
    config = load_config()
    base_dir = Path(config['data']['base_directory'])
    
    timeframe_mapping = {
        "1m": "M1", "5m": "M5", "15m": "M15", "30m": "M30",
        "1h": "H1", "4h": "H4", "1d": "D1"
    }
    
    coverage_data = []
    
    for symbol in config['data']['symbols'][:5]:  # Prvních 5 symbolů
        for timeframe in config['data']['timeframes']:
            folder_name = timeframe_mapping.get(timeframe, timeframe.upper())
            file_path = base_dir / folder_name / f"{symbol}-{folder_name}.parquet"
            
            if file_path.exists():
                try:
                    df = pd.read_parquet(file_path)
                    if len(df) > 0:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        
                        coverage_data.append({
                            'symbol': symbol,
                            'timeframe': folder_name,
                            'start_date': df['timestamp'].min(),
                            'end_date': df['timestamp'].max(),
                            'records': len(df),
                            'days': (df['timestamp'].max() - df['timestamp'].min()).days
                        })
                except Exception as e:
                    print(f"⚠️  Chyba při analýze {file_path}: {e}")
    
    if coverage_data:
        coverage_df = pd.DataFrame(coverage_data)
        
        print("\n📅 Pokrytí dat (prvních 5 symbolů):")
        for symbol in coverage_df['symbol'].unique():
            print(f"\n💰 {symbol}:")
            symbol_data = coverage_df[coverage_df['symbol'] == symbol]
            
            for _, row in symbol_data.iterrows():
                print(f"  {row['timeframe']}: {row['start_date'].date()} - {row['end_date'].date()} "
                      f"({row['records']:,} záznamů, {row['days']} dní)")
    
    return coverage_data


def create_sample_analysis():
    """Vytvoří ukázkovou analýzu dat"""
    print("\n📊 Ukázková analýza BTC-USDT")
    print("=" * 30)
    
    config = load_config()
    base_dir = Path(config['data']['base_directory'])
    
    # Načti BTC data pro různé timeframy
    btc_data = {}
    timeframes = ['H1', 'H4', 'D1']
    
    for tf in timeframes:
        file_path = base_dir / tf / f"BTC-USDT-{tf}.parquet"
        if file_path.exists():
            try:
                df = pd.read_parquet(file_path)
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df = df.set_index('timestamp').sort_index()
                
                # Posledních 30 dní
                recent_data = df[df.index >= df.index.max() - timedelta(days=30)]
                btc_data[tf] = recent_data
                
                print(f"✅ {tf}: {len(recent_data)} záznamů (posledních 30 dní)")
                
            except Exception as e:
                print(f"❌ Chyba při načítání {tf}: {e}")
    
    if not btc_data:
        print("❌ Žádná BTC data nenalezena")
        return
    
    # Základní statistiky
    print(f"\n📈 Základní statistiky (posledních 30 dní):")
    
    for tf, df in btc_data.items():
        if len(df) > 0:
            print(f"\n{tf}:")
            print(f"  Cena: ${df['close'].iloc[-1]:,.2f}")
            print(f"  Min: ${df['low'].min():,.2f}")
            print(f"  Max: ${df['high'].max():,.2f}")
            print(f"  Volatilita: {df['close'].pct_change().std() * 100:.2f}%")
            print(f"  Průměrný objem: {df['volume'].mean():,.0f}")
    
    # Vytvoř jednoduchou vizualizaci (pokud je matplotlib dostupný)
    try:
        if 'D1' in btc_data and len(btc_data['D1']) > 0:
            df = btc_data['D1']
            
            # Jednoduchý graf
            plt.figure(figsize=(12, 6))
            plt.plot(df.index, df['close'], label='Close Price', linewidth=2)
            plt.title('BTC-USDT - Posledních 30 dní (D1)')
            plt.xlabel('Datum')
            plt.ylabel('Cena (USD)')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # Ulož graf
            plt.savefig('btc_analysis.png', dpi=150, bbox_inches='tight')
            print(f"\n📊 Graf uložen jako 'btc_analysis.png'")
            plt.close()
            
    except ImportError:
        print("\n⚠️  Matplotlib není dostupný - graf nebyl vytvořen")
    except Exception as e:
        print(f"\n⚠️  Chyba při vytváření grafu: {e}")


def main():
    """Hlavní funkce"""
    print("🧪 Test a analýza Crypto Lake dat")
    print("=" * 50)
    
    # Test integrity
    integrity_ok = test_data_integrity()
    
    # Analýza pokrytí
    coverage_data = analyze_data_coverage()
    
    # Ukázková analýza
    create_sample_analysis()
    
    print(f"\n🎯 Shrnutí:")
    print(f"✅ Integrita dat: {'OK' if integrity_ok else 'Problémy nalezeny'}")
    print(f"📊 Analyzováno souborů: {len(coverage_data) if coverage_data else 0}")
    
    if integrity_ok:
        print(f"\n🚀 Data jsou připravena k použití!")
        print(f"   Příklad načtení:")
        print(f"   df = pd.read_parquet('crypto_data/H1/BTC-USDT-H1.parquet')")
    
    return 0 if integrity_ok else 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
