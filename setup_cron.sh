#!/bin/bash

# Crypto Lake Downloader - Linux Cron Setup
# Automatické nastavení cron úlohy

set -e

echo "🕒 Nastavení Linux Cron"
echo "======================="

# Získání aktuálního adresáře
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPT_PATH="$SCRIPT_DIR/run.sh"

echo "📁 Adresář projektu: $SCRIPT_DIR"

# Kontrola existence skriptu
if [ ! -f "$SCRIPT_PATH" ]; then
    echo "❌ Soubor run.sh nenalezen!"
    exit 1
fi

# Kontrola spouštěcích oprávnění
if [ ! -x "$SCRIPT_PATH" ]; then
    echo "⚠️  Soubor run.sh nemá spouštěcí oprávnění. Nastavuji..."
    chmod +x "$SCRIPT_PATH"
fi

echo "✅ Skript nalezen: $SCRIPT_PATH"

# Menu pro výběr frekvence
echo ""
echo "Vyberte frekvenci spouštění:"
echo "1. Denn<PERSON> v 2:00"
echo "2. Každých 6 hodin"
echo "3. Každou hodinu (pouze pracovní dny 9-17)"
echo "4. Týdně v neděli v 2:00"
echo "5. Měsíčně 1. den v 2:00"
echo "6. Vlastní nastavení"
echo ""

read -p "Zadejte volbu (1-6): " FREQUENCY

case $FREQUENCY in
    1)
        CRON_SCHEDULE="0 2 * * *"
        FREQ_NAME="Denně v 2:00"
        ;;
    2)
        CRON_SCHEDULE="0 */6 * * *"
        FREQ_NAME="Každých 6 hodin"
        ;;
    3)
        CRON_SCHEDULE="0 9-17 * * 1-5"
        FREQ_NAME="Každou hodinu (pracovní dny 9-17)"
        ;;
    4)
        CRON_SCHEDULE="0 2 * * 0"
        FREQ_NAME="Týdně v neděli v 2:00"
        ;;
    5)
        CRON_SCHEDULE="0 2 1 * *"
        FREQ_NAME="Měsíčně 1. den v 2:00"
        ;;
    6)
        echo ""
        echo "Zadejte vlastní cron výraz (např. '0 2 * * *' pro denně v 2:00):"
        read -p "Cron výraz: " CRON_SCHEDULE
        FREQ_NAME="Vlastní nastavení"
        ;;
    *)
        echo "❌ Neplatná volba!"
        exit 1
        ;;
esac

# Vytvoření cron příkazu
CRON_COMMAND="cd $SCRIPT_DIR && ./run.sh >> logs/cron.log 2>&1"
CRON_ENTRY="$CRON_SCHEDULE $CRON_COMMAND"

echo ""
echo "📋 Shrnutí konfigurace:"
echo "   Frekvence: $FREQ_NAME"
echo "   Cron výraz: $CRON_SCHEDULE"
echo "   Příkaz: $CRON_COMMAND"
echo "   Kompletní záznam: $CRON_ENTRY"
echo ""

read -p "Pokračovat s přidáním do crontab? (y/N): " CONFIRM
if [[ ! "$CONFIRM" =~ ^[Yy]$ ]]; then
    echo "Zrušeno uživatelem."
    exit 0
fi

echo ""
echo "🔧 Přidávání do crontab..."

# Vytvoření zálohy současného crontab
BACKUP_FILE="$SCRIPT_DIR/crontab_backup_$(date +%Y%m%d_%H%M%S)"
crontab -l > "$BACKUP_FILE" 2>/dev/null || echo "# Nový crontab" > "$BACKUP_FILE"
echo "💾 Záloha crontab uložena: $BACKUP_FILE"

# Kontrola, zda už záznam neexistuje
if crontab -l 2>/dev/null | grep -q "Crypto.*[Dd]ownloader\|crypto.*downloader"; then
    echo "⚠️  Nalezen existující záznam pro Crypto Downloader!"
    echo ""
    echo "Existující záznamy:"
    crontab -l 2>/dev/null | grep -n "Crypto.*[Dd]ownloader\|crypto.*downloader" || true
    echo ""
    read -p "Chcete odstranit existující záznamy? (y/N): " REMOVE_EXISTING
    
    if [[ "$REMOVE_EXISTING" =~ ^[Yy]$ ]]; then
        # Odstranění existujících záznamů
        crontab -l 2>/dev/null | grep -v "Crypto.*[Dd]ownloader\|crypto.*downloader" | crontab -
        echo "✅ Existující záznamy odstraněny"
    fi
fi

# Přidání nového záznamu
(crontab -l 2>/dev/null; echo "# Crypto Lake Downloader - $FREQ_NAME"; echo "$CRON_ENTRY") | crontab -

if [ $? -eq 0 ]; then
    echo "✅ Cron úloha úspěšně přidána!"
    echo ""
    echo "📊 Aktuální crontab:"
    crontab -l | tail -5
    
    # Kontrola cron služby
    echo ""
    echo "🔍 Kontrola cron služby..."
    if systemctl is-active --quiet cron 2>/dev/null; then
        echo "✅ Cron služba běží"
    elif systemctl is-active --quiet crond 2>/dev/null; then
        echo "✅ Crond služba běží"
    else
        echo "⚠️  Cron služba neběží nebo není dostupná"
        echo "   Zkuste: sudo systemctl start cron"
        echo "   Nebo:   sudo systemctl start crond"
    fi
    
    # Vytvoření logs složky
    mkdir -p "$SCRIPT_DIR/logs"
    
    echo ""
    echo "🧪 Chcete otestovat úlohu nyní?"
    read -p "Spustit test? (y/N): " TEST_NOW
    if [[ "$TEST_NOW" =~ ^[Yy]$ ]]; then
        echo ""
        echo "🚀 Spouštím test..."
        cd "$SCRIPT_DIR"
        ./run.sh
        echo ""
        echo "✅ Test dokončen. Zkontrolujte výstup výše."
    fi
    
    echo ""
    echo "📝 Užitečné příkazy:"
    echo "   Zobrazení crontab:     crontab -l"
    echo "   Editace crontab:       crontab -e"
    echo "   Smazání crontab:       crontab -r"
    echo "   Kontrola cron logů:    tail -f /var/log/cron"
    echo "   Kontrola app logů:     tail -f $SCRIPT_DIR/logs/cron.log"
    echo "   Manuální spuštění:     cd $SCRIPT_DIR && ./run.sh"
    
else
    echo "❌ Chyba při přidávání cron úlohy!"
    echo "   Obnovuji zálohu..."
    crontab "$BACKUP_FILE"
    exit 1
fi

echo ""
echo "🎉 Nastavení dokončeno!"

# Volitelné: nastavení dalších úloh
echo ""
read -p "Chcete přidat úlohu pro čištění starých logů? (y/N): " ADD_CLEANUP
if [[ "$ADD_CLEANUP" =~ ^[Yy]$ ]]; then
    CLEANUP_ENTRY="0 1 * * 0 find $SCRIPT_DIR/logs/ -name '*.log' -mtime +7 -delete"
    (crontab -l; echo "# Crypto Lake Downloader - Týdenní čištění logů"; echo "$CLEANUP_ENTRY") | crontab -
    echo "✅ Přidána úloha pro čištění logů (neděle v 1:00)"
fi

read -p "Chcete přidat úlohu pro měsíční backup? (y/N): " ADD_BACKUP
if [[ "$ADD_BACKUP" =~ ^[Yy]$ ]]; then
    BACKUP_ENTRY="0 3 1 * * cd $SCRIPT_DIR && tar -czf backups/monthly_\$(date +\\%Y\\%m).tar.gz crypto_data/"
    (crontab -l; echo "# Crypto Lake Downloader - Měsíční backup"; echo "$BACKUP_ENTRY") | crontab -
    echo "✅ Přidána úloha pro měsíční backup (1. den v měsíci v 3:00)"
    mkdir -p "$SCRIPT_DIR/backups"
fi

echo ""
echo "🏁 Vše je nastaveno! Cron bude automaticky spouštět stahování podle nastaveného rozvrhu."
