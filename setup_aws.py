#!/usr/bin/env python3
"""
Pomocný skript pro nastavení AWS credentials pro Crypto Lake
"""

import os
import sys
from pathlib import Path


def setup_aws_credentials():
    """Nastavení AWS credentials"""
    print("🔧 Nastavení AWS Credentials pro Crypto Lake")
    print("=" * 50)
    
    # <PERSON><PERSON><PERSON><PERSON> credentials od uživatele
    print("Zadejte vaše AWS credentials z crypto-lake.com:")
    access_key = input("AWS Access Key ID: ").strip()
    secret_key = input("AWS Secret Access Key: ").strip()
    
    if not access_key or not secret_key:
        print("❌ Chyba: Musíte zadat oba klíče!")
        return False
    
    # Určení cesty k AWS config
    home_dir = Path.home()
    aws_dir = home_dir / ".aws"
    credentials_file = aws_dir / "credentials"
    config_file = aws_dir / "config"
    
    # Vytvoř .aws složku pokud neexistuje
    aws_dir.mkdir(exist_ok=True)
    
    # Vyt<PERSON><PERSON> credentials soubor
    credentials_content = f"""[default]
aws_access_key_id = {access_key}
aws_secret_access_key = {secret_key}
"""
    
    # Vytvoř config soubor
    config_content = """[default]
region = eu-west-1
"""
    
    try:
        # Zálohuj existující soubory
        if credentials_file.exists():
            backup_file = credentials_file.with_suffix('.backup')
            credentials_file.rename(backup_file)
            print(f"📋 Záloha credentials: {backup_file}")
        
        if config_file.exists():
            backup_file = config_file.with_suffix('.backup')
            config_file.rename(backup_file)
            print(f"📋 Záloha config: {backup_file}")
        
        # Zapíš nové soubory
        with open(credentials_file, 'w') as f:
            f.write(credentials_content)
        
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        # Nastav správná oprávnění (pouze pro Unix systémy)
        if os.name != 'nt':  # Není Windows
            os.chmod(credentials_file, 0o600)
            os.chmod(config_file, 0o600)
        
        print(f"✅ AWS credentials nastaveny:")
        print(f"   📁 {credentials_file}")
        print(f"   📁 {config_file}")
        print(f"   🌍 Region: eu-west-1")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při ukládání: {e}")
        return False


def test_aws_connection():
    """Test AWS připojení"""
    print("\n🧪 Test AWS připojení...")

    try:
        import boto3
        from botocore.exceptions import ClientError, NoCredentialsError

        # Vytvoř S3 klienta
        s3_client = boto3.client('s3', region_name='eu-west-1')

        # Test připojení k crypto-lake bucketu
        s3_client.head_bucket(Bucket='crypto-lake')

        print("✅ AWS připojení úspěšné!")
        print("✅ Přístup k crypto-lake bucketu potvrzen!")

        return True

    except ImportError:
        print("❌ boto3 knihovna není nainstalována!")
        print("   Spusťte: pip install -r requirements.txt")
        return False
    except Exception as e:
        # Pokud boto3 není dostupný, nemůžeme importovat specifické exceptions
        try:
            from botocore.exceptions import ClientError, NoCredentialsError

            if isinstance(e, NoCredentialsError):
                print("❌ AWS credentials nenalezeny!")
                return False
            elif isinstance(e, ClientError):
                error_code = e.response['Error']['Code']
                if error_code == '403':
                    print("❌ Přístup zamítnut - zkontrolujte credentials!")
                elif error_code == '404':
                    print("❌ Bucket 'crypto-lake' nenalezen!")
                else:
                    print(f"❌ AWS chyba: {e}")
                return False
        except ImportError:
            pass

        print(f"❌ Chyba při testování připojení: {e}")
        return False


def main():
    """Hlavní funkce"""
    print("Crypto Lake AWS Setup")
    print("=" * 30)
    
    # Nastavení credentials
    if setup_aws_credentials():
        # Test připojení
        if test_aws_connection():
            print("\n🎉 Vše je připraveno!")
            print("   Nyní můžete spustit: python crypto_downloader.py")
        else:
            print("\n⚠️  Credentials nastaveny, ale test připojení selhal.")
            print("   Zkontrolujte správnost klíčů.")
    else:
        print("\n❌ Nastavení selhalo!")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
