# Crypto Lake Downloader - PowerShell Script
# Autor: Crypto Lake Downloader
# Verze: 1.0

param(
    [switch]$Silent = $false,
    [switch]$NoBackup = $false
)

# Nastavení error handling
$ErrorActionPreference = "Stop"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    if (-not $Silent) {
        Write-Host $Message -ForegroundColor $Color
    }
}

function Test-PythonInstallation {
    try {
        $pythonVersion = python --version 2>&1
        if ($LASTEXITCODE -ne 0) {
            throw "Python není nainstalován"
        }
        return $pythonVersion
    }
    catch {
        Write-ColorOutput "❌ Python není nainstalován nebo není v PATH!" "Red"
        Write-ColorOutput "   Stáhněte Python z https://python.org" "Yellow"
        exit 1
    }
}

function Test-Dependencies {
    Write-ColorOutput "📦 Kontrola závislostí..." "<PERSON>an"
    
    try {
        python -c "import lakeapi, pandas, pyarrow, yaml, tqdm" 2>$null
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "⚠️  Některé závislosti chybí. Instaluji..." "Yellow"
            pip install -r requirements.txt
            if ($LASTEXITCODE -ne 0) {
                throw "Chyba při instalaci závislostí"
            }
        }
    }
    catch {
        Write-ColorOutput "❌ Chyba při kontrole/instalaci závislostí: $_" "Red"
        exit 1
    }
}

function Test-AWSCredentials {
    Write-ColorOutput "🔐 Kontrola AWS credentials..." "Cyan"
    
    $credentialsPath = "$env:USERPROFILE\.aws\credentials"
    if (-not (Test-Path $credentialsPath)) {
        Write-ColorOutput "⚠️  AWS credentials nenalezeny!" "Yellow"
        Write-ColorOutput "   Spusťte: python setup_aws.py" "Yellow"
        exit 1
    }
}

function Test-Configuration {
    Write-ColorOutput "⚙️  Kontrola konfigurace..." "Cyan"
    
    if (-not (Test-Path "config.yaml")) {
        Write-ColorOutput "❌ Konfigurační soubor config.yaml nenalezen!" "Red"
        exit 1
    }
}

function New-BackupDirectory {
    if ($NoBackup) {
        return $null
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupDir = "backups\$timestamp"
    
    New-Item -ItemType Directory -Path $backupDir -Force | Out-Null
    
    if (Test-Path "crypto_data") {
        Write-ColorOutput "💾 Zálohuji existující data do $backupDir..." "Cyan"
        Copy-Item -Path "crypto_data" -Destination "$backupDir\crypto_data" -Recurse -Force
    }
    
    return $backupDir
}

function Start-CryptoDownloader {
    param([string]$LogFile)
    
    Write-ColorOutput "⬇️  Spouštím stahování dat..." "Green"
    Write-ColorOutput "   Čas spuštění: $(Get-Date)" "Gray"
    
    # Spuštění s logováním
    $startTime = Get-Date
    
    try {
        python crypto_downloader.py 2>&1 | Tee-Object -FilePath $LogFile
        $exitCode = $LASTEXITCODE
    }
    catch {
        Write-ColorOutput "❌ Chyba při spuštění: $_" "Red"
        $exitCode = 1
    }
    
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    return @{
        ExitCode = $exitCode
        StartTime = $startTime
        EndTime = $endTime
        Duration = $duration
    }
}

function Show-Statistics {
    Write-ColorOutput "`n📈 Rychlé statistiky:" "Cyan"
    
    if (Test-Path "crypto_data") {
        $folders = (Get-ChildItem "crypto_data" -Directory).Count
        $files = (Get-ChildItem "crypto_data" -Recurse -Filter "*.parquet").Count
        $size = (Get-ChildItem "crypto_data" -Recurse | Measure-Object -Property Length -Sum).Sum
        $sizeGB = [math]::Round($size / 1GB, 2)
        
        Write-ColorOutput "   Složky: $folders" "White"
        Write-ColorOutput "   Soubory: $files" "White"
        Write-ColorOutput "   Velikost: $sizeGB GB" "White"
    }
}

# Hlavní funkce
function Main {
    Write-ColorOutput "🚀 Crypto Lake Downloader - PowerShell spuštění" "Green"
    Write-ColorOutput "================================================" "Green"
    
    # Kontroly
    $pythonVersion = Test-PythonInstallation
    Write-ColorOutput "🐍 Python verze: $($pythonVersion -replace 'Python ', '')" "Green"
    
    Test-Dependencies
    Test-AWSCredentials
    Test-Configuration
    
    # Vytvoření složek
    @("logs", "backups") | ForEach-Object {
        if (-not (Test-Path $_)) {
            New-Item -ItemType Directory -Path $_ -Force | Out-Null
        }
    }
    
    # Backup
    $backupDir = New-BackupDirectory
    
    # Log soubor
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $logFile = "logs\crypto_downloader_$timestamp.log"
    
    # Spuštění
    $result = Start-CryptoDownloader -LogFile $logFile
    
    # Výsledky
    Write-ColorOutput "`n📊 Shrnutí spuštění:" "Cyan"
    Write-ColorOutput "   Čas dokončení: $($result.EndTime)" "White"
    Write-ColorOutput "   Doba trvání: $($result.Duration.ToString('hh\:mm\:ss'))" "White"
    Write-ColorOutput "   Exit kód: $($result.ExitCode)" "White"
    Write-ColorOutput "   Log soubor: $logFile" "White"
    
    if ($result.ExitCode -eq 0) {
        Write-ColorOutput "✅ Stahování dokončeno úspěšně!" "Green"
        Show-Statistics
    } else {
        Write-ColorOutput "❌ Stahování selhalo s kódem $($result.ExitCode)" "Red"
        Write-ColorOutput "   Zkontrolujte log: $logFile" "Yellow"
    }
    
    return $result.ExitCode
}

# Spuštění
try {
    $exitCode = Main
    exit $exitCode
}
catch {
    Write-ColorOutput "❌ Kritická chyba: $_" "Red"
    exit 1
}
