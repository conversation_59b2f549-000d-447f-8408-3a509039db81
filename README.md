# Crypto Lake Downloader

🚀 **Profesionální nástroj pro stahování krypto dat z crypto-lake.com**

Tento Python skript umožňuje automatické stahování OHLCV dat pro TOP 20 krypto párů s podporou paralelního stahování, inkrementálního updatu a ukládání do formátu Parquet.

## ✨ Funkce

- 📊 **OHLCV Data**: Stahování Open, High, Low, Close, Volume dat
- ⚡ **Paralelní stahování**: Více vláken pro rychlejší stahování
- 🔄 **Inkrementální update**: Stahuje pouze nová data při opakovaném spuštění
- 📁 **Organizovaná struktura**: Automatické vytváření složek podle timeframů
- 💾 **Parquet formát**: Efektivní komprese a rychlé načítání
- 📝 **Detailní logging**: Sledování prů<PERSON> a chyb
- 🛠️ **Konfigurovatelné**: <PERSON><PERSON><PERSON><PERSON> nastavení přes YAML soubor

## 📋 Požadavky

- Python 3.8+
- A<PERSON> credentials pro crypto-lake.com
- Internetové připojení

## 🚀 Rychlé spuštění

### 1. Instalace závislostí

```bash
pip3 install -r requirements.txt
```

Nebo použijte Makefile:

```bash
make install
```

### 2. Nastavení AWS credentials

Spusťte pomocný skript pro nastavení:

```bash
python3 setup_aws.py
```

Nebo:

```bash
make setup
```

Nebo manuálně vytvořte soubory:

**~/.aws/credentials:**
```ini
[default]
aws_access_key_id = VÁŠ_ACCESS_KEY
aws_secret_access_key = VÁŠ_SECRET_KEY
```

**~/.aws/config:**
```ini
[default]
region = eu-west-1
```

### 3. Konfigurace

Upravte `config.yaml` podle vašich potřeb:

```yaml
# Základní složka pro data
data:
  base_directory: "./crypto_data"
  start_date: "2018-01-01"
  
# Symboly k stahování (TOP 20)
symbols:
  - "BTC-USDT"
  - "ETH-USDT"
  # ... další

# Timeframy
timeframes:
  - "1m"   # M1
  - "5m"   # M5
  - "15m"  # M15
  - "30m"  # M30
  - "1h"   # H1
  - "4h"   # H4
  - "1d"   # D1
```

### 4. Spuštění

```bash
python3 crypto_downloader.py
```

Nebo:

```bash
make download
```

## 📁 Struktura souborů

Po spuštění se vytvoří následující struktura:

```
crypto_data/
├── M1/
│   ├── BTC-USDT-M1.parquet
│   ├── ETH-USDT-M1.parquet
│   └── ...
├── M5/
│   ├── BTC-USDT-M5.parquet
│   └── ...
├── H1/
│   ├── BTC-USDT-H1.parquet
│   └── ...
└── D1/
    ├── BTC-USDT-D1.parquet
    └── ...
```

## ⚙️ Konfigurace

### Základní nastavení

- **base_directory**: Složka pro ukládání dat
- **start_date**: Datum od kterého stahovat (YYYY-MM-DD)
- **symbols**: Seznam krypto párů
- **timeframes**: Seznam timeframů

### Stahování

- **max_workers**: Počet paralelních vláken (default: 4)
- **timeout**: Timeout pro stahování v sekundách
- **max_retries**: Počet opakování při chybě

### Logging

- **level**: Úroveň logování (DEBUG, INFO, WARNING, ERROR)
- **log_file**: Soubor pro logy
- **max_log_size_mb**: Maximální velikost log souboru

## 🔄 Inkrementální update

Skript automaticky detekuje existující data a stahuje pouze nová data od posledního záznamu. To umožňuje:

- Rychlejší opakované spuštění
- Úsporu bandwidth
- Automatickou aktualizaci dat

## 📊 Formát dat

Každý Parquet soubor obsahuje sloupce:

- **timestamp**: Časové razítko (datetime)
- **open**: Otevírací cena
- **high**: Nejvyšší cena
- **low**: Nejnižší cena
- **close**: Zavírací cena
- **volume**: Objem obchodů

## 🛠️ Použití v kódu

```python
import pandas as pd

# Načtení dat
df = pd.read_parquet('crypto_data/H1/BTC-USDT-H1.parquet')

# Základní statistiky
print(f"Záznamů: {len(df)}")
print(f"Od: {df['timestamp'].min()}")
print(f"Do: {df['timestamp'].max()}")

# Analýza
print(df.describe())
```

## 🐛 Řešení problémů

### AWS Credentials chyby

```bash
# Zkontrolujte credentials
python setup_aws.py

# Nebo manuálně
cat ~/.aws/credentials
```

### Chyby stahování

- Zkontrolujte internetové připojení
- Ověřte platnost AWS klíčů
- Zkontrolujte logy v `crypto_downloader.log`

### Nedostatek místa

- Parquet soubory jsou komprimované, ale stále mohou být velké
- Zvažte stahování menšího počtu symbolů nebo timeframů

## 📈 Výkon

- **Paralelní stahování**: 4 vlákna současně (konfigurovatelné)
- **Komprese**: Parquet formát šetří ~70% místa oproti CSV
- **Rychlost**: Typicky 10-50 MB/s v závislosti na připojení

## 🤝 Podpora

Pro problémy nebo dotazy:

1. Zkontrolujte logy v `crypto_downloader.log`
2. Ověřte konfiguraci v `config.yaml`
3. Spusťte `python setup_aws.py` pro test připojení

## 🚀 Produkční nasazení

Pro produkční nasazení viz [PRODUCTION.md](PRODUCTION.md) s detailními instrukcemi pro:

- Automatické spouštění (cron)
- Monitoring a údržbu
- Optimalizaci výkonu
- Řešení problémů
- Škálování

### Rychlé produkční spuštění:

```bash
# Kompletní setup
make quickstart

# Nebo krok za krokem
make install
make setup
make download

# Produkční spuštění s logováním
./run.sh                    # Linux/macOS
run.bat                     # Windows
```

### Automatické spouštění:

```bash
# Linux/macOS - nastavení cron
make schedule
# nebo
./setup_cron.sh

# Windows - nastavení Task Scheduler
setup_scheduler.bat         # Spustit jako Administrator
```

Detailní instrukce viz [SCHEDULING.md](SCHEDULING.md)

## 📊 Monitoring

```bash
# Zobrazení statistik
make stats

# Kontrola logů
tail -f crypto_downloader.log

# Struktura dat
make tree
```

## 📄 Licence

Tento projekt je určen pro osobní použití s daty z crypto-lake.com.

## 🔗 Odkazy

- [Crypto Lake](https://crypto-lake.com) - Oficiální web
- [Lake API dokumentace](https://github.com/crypto-lake/lake-api)
- [PRODUCTION.md](PRODUCTION.md) - Produkční nasazení
