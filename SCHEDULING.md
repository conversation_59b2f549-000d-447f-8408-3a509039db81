# Automatické spou<PERSON>tění - Linux & Windows

## 🐧 Linux - Cron

### 1. Základn<PERSON> nasta<PERSON>

```bash
# Editace crontab
crontab -e

# <PERSON><PERSON>id<PERSON>í úlohy pro denní spuštění v 2:00
0 2 * * * cd /path/to/Crypto-lake_downloader && ./run.sh >> logs/cron.log 2>&1

# Přidání úlohy pro spuštění kaž<PERSON> 6 hodin
0 */6 * * * cd /path/to/Crypto-lake_downloader && ./run.sh >> logs/cron.log 2>&1

# Přidání úlohy pro spuštění každou hodinu (pouze v pracovní dny)
0 * * * 1-5 cd /path/to/Crypto-lake_downloader && ./run.sh >> logs/cron.log 2>&1
```

### 2. Pokročilé cron nastavení

```bash
# Vytvoření cron souboru
cat > crypto_downloader.cron << 'EOF'
# Crypto Lake Downloader - Cron konfigurace
# Denn<PERSON> spu<PERSON> v 2:00
0 2 * * * cd /home/<USER>/Crypto-lake_downloader && ./run.sh >> logs/cron.log 2>&1

# Týdenní čištění logů (neděle v 1:00)
0 1 * * 0 cd /home/<USER>/Crypto-lake_downloader && find logs/ -name "*.log" -mtime +7 -delete

# Měsíční backup (1. den v měsíci v 3:00)
0 3 1 * * cd /home/<USER>/Crypto-lake_downloader && tar -czf backups/monthly_$(date +\%Y\%m).tar.gz crypto_data/
EOF

# Instalace cron souboru
crontab crypto_downloader.cron

# Kontrola nastavení
crontab -l
```

### 3. Systemd timer (alternativa k cron)

```bash
# Vytvoření service souboru
sudo tee /etc/systemd/system/crypto-downloader.service << 'EOF'
[Unit]
Description=Crypto Lake Downloader
After=network.target

[Service]
Type=oneshot
User=your-username
WorkingDirectory=/path/to/Crypto-lake_downloader
ExecStart=/path/to/Crypto-lake_downloader/run.sh
StandardOutput=append:/path/to/Crypto-lake_downloader/logs/systemd.log
StandardError=append:/path/to/Crypto-lake_downloader/logs/systemd.log
EOF

# Vytvoření timer souboru
sudo tee /etc/systemd/system/crypto-downloader.timer << 'EOF'
[Unit]
Description=Run Crypto Lake Downloader daily
Requires=crypto-downloader.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
EOF

# Aktivace a spuštění
sudo systemctl daemon-reload
sudo systemctl enable crypto-downloader.timer
sudo systemctl start crypto-downloader.timer

# Kontrola stavu
sudo systemctl status crypto-downloader.timer
sudo systemctl list-timers crypto-downloader.timer
```

## 🪟 Windows - Task Scheduler

### 1. Grafické rozhraní (GUI)

1. **Otevřete Task Scheduler:**
   - Stiskněte `Win + R`, zadejte `taskschd.msc`
   - Nebo vyhledejte "Task Scheduler" v Start menu

2. **Vytvoření nové úlohy:**
   - Klikněte na "Create Basic Task" nebo "Create Task"
   - Název: `Crypto Lake Downloader`
   - Popis: `Automatické stahování krypto dat`

3. **Nastavení spouštěče (Trigger):**
   - **Denně:** Daily at 2:00 AM
   - **Týdně:** Weekly on Sunday at 2:00 AM
   - **Při spuštění:** At startup (s 5min zpožděním)

4. **Nastavení akce (Action):**
   - **Program:** `C:\path\to\Crypto-lake_downloader\run.bat`
   - **Start in:** `C:\path\to\Crypto-lake_downloader`

5. **Pokročilé nastavení:**
   - ✅ Run whether user is logged on or not
   - ✅ Run with highest privileges
   - ✅ Hidden
   - ⚙️ Configure for: Windows 10

### 2. PowerShell příkazová řádka

```powershell
# Vytvoření úlohy pro denní spuštění
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\path\to\Crypto-lake_downloader\run.ps1 -Silent" -WorkingDirectory "C:\path\to\Crypto-lake_downloader"

$trigger = New-ScheduledTaskTrigger -Daily -At "2:00AM"

$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -RunOnlyIfNetworkAvailable

$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

Register-ScheduledTask -TaskName "Crypto Lake Downloader" -Action $action -Trigger $trigger -Settings $settings -Principal $principal -Description "Automatické stahování krypto dat z crypto-lake.com"

# Kontrola úlohy
Get-ScheduledTask -TaskName "Crypto Lake Downloader"

# Spuštění úlohy manuálně
Start-ScheduledTask -TaskName "Crypto Lake Downloader"

# Zobrazení historie
Get-ScheduledTaskInfo -TaskName "Crypto Lake Downloader"
```

### 3. Batch skript pro registraci

```batch
@echo off
REM Registrace úlohy v Task Scheduler

set TASK_NAME="Crypto Lake Downloader"
set SCRIPT_PATH=%~dp0run.bat
set WORKING_DIR=%~dp0

echo Registruji úlohu %TASK_NAME%...

schtasks /create ^
    /tn %TASK_NAME% ^
    /tr "\"%SCRIPT_PATH%\"" ^
    /sc daily ^
    /st 02:00 ^
    /ru SYSTEM ^
    /rl HIGHEST ^
    /f

if %errorlevel% equ 0 (
    echo ✅ Úloha úspěšně zaregistrována!
    echo    Název: %TASK_NAME%
    echo    Spuštění: Denně v 2:00
    echo    Skript: %SCRIPT_PATH%
) else (
    echo ❌ Chyba při registraci úlohy!
)

pause
```

## ⚙️ Pokročilé konfigurace

### 1. Různé frekvence spouštění

#### Linux Cron formáty:
```bash
# Každou hodinu
0 * * * * /path/to/script

# Každé 6 hodin
0 */6 * * * /path/to/script

# Denně v 2:00
0 2 * * * /path/to/script

# Týdně v neděli v 2:00
0 2 * * 0 /path/to/script

# Měsíčně 1. den v 2:00
0 2 1 * * /path/to/script

# Pouze v pracovní dny v 9:00
0 9 * * 1-5 /path/to/script

# Každých 15 minut během obchodních hodin
*/15 9-17 * * 1-5 /path/to/script
```

#### Windows Task Scheduler:
- **Minuty:** Every 15 minutes
- **Hodiny:** Every 6 hours
- **Denně:** Daily at specific time
- **Týdně:** Weekly on specific days
- **Měsíčně:** Monthly on specific date
- **Při události:** On system startup/login

### 2. Podmíněné spouštění

#### Linux s kontrolou připojení:
```bash
#!/bin/bash
# Kontrola internetového připojení před spuštěním
if ping -c 1 google.com &> /dev/null; then
    cd /path/to/Crypto-lake_downloader && ./run.sh
else
    echo "$(date): Žádné internetové připojení" >> logs/connection_errors.log
fi
```

#### Windows PowerShell s kontrolou:
```powershell
# Kontrola připojení a volného místa
if ((Test-NetConnection -ComputerName "*******" -Port 53).TcpTestSucceeded) {
    $freeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='C:'").FreeSpace / 1GB
    if ($freeSpace -gt 10) {
        & "C:\path\to\Crypto-lake_downloader\run.ps1" -Silent
    } else {
        Write-EventLog -LogName Application -Source "Crypto Downloader" -EventId 1001 -Message "Nedostatek místa na disku: $freeSpace GB"
    }
}
```

## 📊 Monitoring automatických spuštění

### 1. Linux monitoring

```bash
# Kontrola cron logů
tail -f /var/log/cron
grep "crypto" /var/log/cron

# Kontrola aplikačních logů
tail -f logs/cron.log
tail -f logs/crypto_downloader_*.log

# Systemd monitoring
journalctl -u crypto-downloader.service -f
systemctl status crypto-downloader.timer
```

### 2. Windows monitoring

```powershell
# Kontrola Task Scheduler historie
Get-ScheduledTaskInfo -TaskName "Crypto Lake Downloader"

# Kontrola Event Logu
Get-WinEvent -FilterHashtable @{LogName='Microsoft-Windows-TaskScheduler/Operational'; ID=200,201} | Where-Object {$_.Message -like "*Crypto Lake*"}

# Kontrola aplikačních logů
Get-Content "logs\crypto_downloader_*.log" -Tail 50
```

## 🚨 Řešení problémů

### Časté problémy:

1. **Cron nespouští úlohu:**
   ```bash
   # Kontrola cron služby
   sudo systemctl status cron
   
   # Kontrola syntaxe crontab
   crontab -l
   
   # Kontrola oprávnění
   ls -la run.sh
   chmod +x run.sh
   ```

2. **Windows Task Scheduler selhává:**
   - Zkontrolujte oprávnění uživatele
   - Ověřte cestu ke skriptu
   - Zkontrolujte "Run whether user is logged on or not"
   - Nastavte "Run with highest privileges"

3. **Skripty nenaleznou Python:**
   ```bash
   # Linux - přidání PATH do crontab
   0 2 * * * export PATH=/usr/local/bin:$PATH && cd /path/to/project && ./run.sh
   ```
   
   ```batch
   REM Windows - plná cesta k Python
   "C:\Python39\python.exe" crypto_downloader.py
   ```

## 📋 Checklist pro nastavení

### Linux:
- [ ] Skript `run.sh` má spouštěcí oprávnění (`chmod +x`)
- [ ] Crontab je správně nakonfigurován
- [ ] Cesty v crontab jsou absolutní
- [ ] Logování je nastaveno
- [ ] Testováno manuální spuštění

### Windows:
- [ ] PowerShell execution policy povoluje skripty
- [ ] Task Scheduler úloha je vytvořena
- [ ] Oprávnění jsou správně nastavena
- [ ] Cesty jsou absolutní
- [ ] Testováno manuální spuštění

### Oba systémy:
- [ ] AWS credentials jsou nastaveny
- [ ] Internetové připojení je stabilní
- [ ] Dostatečné místo na disku
- [ ] Monitoring je nakonfigurován
- [ ] Zálohy jsou nastaveny
