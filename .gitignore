# Crypto Lake Downloader .gitignore

# Data složky
crypto_data/
data/
*.parquet

# Log soubory
*.log
*.log.*

# AWS credentials (NIKDY necommitovat!)
.aws/
aws_credentials.txt
credentials.txt

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Jupyter Notebook
.ipynb_checkpoints

# Matplotlib
*.png
*.jpg
*.jpeg
*.pdf

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Config backups
config.yaml.backup
config.yaml.bak

# Test outputs
test_output/
analysis_output/

# Production data
*.csv
*.json
*.xlsx
