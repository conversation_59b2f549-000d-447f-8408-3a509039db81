#!/bin/bash

# Crypto Lake Downloader - Produk<PERSON><PERSON><PERSON> spo<PERSON><PERSON><PERSON><PERSON> skript
# Autor: Crypto Lake Downloader
# Verze: 1.0

set -e  # Ukončit při chybě

echo "🚀 Crypto Lake Downloader - <PERSON>duk<PERSON><PERSON><PERSON> spu<PERSON>n<PERSON>"
echo "================================================"

# Kontrola Python verze
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 není nainstalován!"
    exit 1
fi

PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
echo "🐍 Python verze: $PYTHON_VERSION"

# Kontrola závislostí
echo "📦 Kontrola závislostí..."
if ! python3 -c "import lakeapi, pandas, pyarrow, yaml, tqdm" 2>/dev/null; then
    echo "⚠️  Některé zá<PERSON>losti chybí. Instaluji..."
    pip3 install -r requirements.txt
fi

# Kontrola AWS credentials
echo "🔐 Kontrola AWS credentials..."
if [ ! -f ~/.aws/credentials ]; then
    echo "⚠️  AWS credentials nenalezeny!"
    echo "   Spusťte: python3 setup_aws.py"
    echo "   Nebo: make setup"
    exit 1
fi

# Kontrola konfigurace
echo "⚙️  Kontrola konfigurace..."
if [ ! -f config.yaml ]; then
    echo "❌ Konfigurační soubor config.yaml nenalezen!"
    exit 1
fi

# Vytvoření backup složky
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup existujících dat (pokud existují)
if [ -d "crypto_data" ]; then
    echo "💾 Zálohuji existující data do $BACKUP_DIR..."
    cp -r crypto_data "$BACKUP_DIR/" 2>/dev/null || true
fi

# Spuštění downloaderu
echo "⬇️  Spouštím stahování dat..."
echo "   Čas spuštění: $(date)"
echo "   PID: $$"

# Logování do souboru
LOG_FILE="logs/crypto_downloader_$(date +%Y%m%d_%H%M%S).log"
mkdir -p logs

# Spuštění s logováním
python3 crypto_downloader.py 2>&1 | tee "$LOG_FILE"

EXIT_CODE=${PIPESTATUS[0]}

echo ""
echo "📊 Shrnutí spuštění:"
echo "   Čas dokončení: $(date)"
echo "   Exit kód: $EXIT_CODE"
echo "   Log soubor: $LOG_FILE"

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ Stahování dokončeno úspěšně!"
    
    # Zobrazení statistik
    echo ""
    echo "📈 Rychlé statistiky:"
    if [ -d "crypto_data" ]; then
        echo "   Složky: $(find crypto_data -type d | wc -l)"
        echo "   Soubory: $(find crypto_data -name "*.parquet" | wc -l)"
        echo "   Velikost: $(du -sh crypto_data 2>/dev/null | cut -f1)"
    fi
else
    echo "❌ Stahování selhalo s kódem $EXIT_CODE"
    echo "   Zkontrolujte log: $LOG_FILE"
fi

exit $EXIT_CODE
