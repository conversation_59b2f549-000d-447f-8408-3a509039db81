@echo off
REM Crypto Lake Downloader - Windows Task Scheduler Setup
REM Automatické nastavení úlohy v Task Scheduler

echo 🕒 Nastavení Windows Task Scheduler
echo =====================================

REM Kontrola admin oprávnění
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Tento skript vyžaduje admin oprávnění!
    echo    Spusťte jako Administrator
    pause
    exit /b 1
)

REM Získání aktuálního adres<PERSON>ře
set SCRIPT_DIR=%~dp0
set TASK_NAME=Crypto Lake Downloader
set BATCH_SCRIPT=%SCRIPT_DIR%run.bat
set PS_SCRIPT=%SCRIPT_DIR%run.ps1

echo 📁 Adresář projektu: %SCRIPT_DIR%
echo 🎯 Název úlohy: %TASK_NAME%

REM Kontrola existence skriptů
if not exist "%BATCH_SCRIPT%" (
    echo ❌ Soubor run.bat nenalezen!
    pause
    exit /b 1
)

if not exist "%PS_SCRIPT%" (
    echo ❌ Soubor run.ps1 nenalezen!
    pause
    exit /b 1
)

echo.
echo Vyberte typ skriptu:
echo 1. Batch skript (run.bat) - jednodušší
echo 2. PowerShell skript (run.ps1) - pokročilejší
echo.
set /p SCRIPT_TYPE="Zadejte volbu (1 nebo 2): "

if "%SCRIPT_TYPE%"=="1" (
    set SELECTED_SCRIPT=%BATCH_SCRIPT%
    set SCRIPT_TYPE_NAME=Batch
) else if "%SCRIPT_TYPE%"=="2" (
    set SELECTED_SCRIPT=PowerShell.exe
    set SCRIPT_ARGS=-ExecutionPolicy Bypass -File "%PS_SCRIPT%" -Silent
    set SCRIPT_TYPE_NAME=PowerShell
) else (
    echo ❌ Neplatná volba!
    pause
    exit /b 1
)

echo.
echo Vyberte frekvenci spouštění:
echo 1. Denně v 2:00
echo 2. Každých 6 hodin
echo 3. Týdně v neděli v 2:00
echo 4. Při spuštění systému (s 5min zpožděním)
echo 5. Vlastní nastavení
echo.
set /p FREQUENCY="Zadejte volbu (1-5): "

REM Nastavení parametrů podle volby
if "%FREQUENCY%"=="1" (
    set SCHEDULE_TYPE=DAILY
    set START_TIME=02:00
    set MODIFIER=
    set FREQ_NAME=Denně v 2:00
) else if "%FREQUENCY%"=="2" (
    set SCHEDULE_TYPE=HOURLY
    set START_TIME=00:00
    set MODIFIER=/mo 6
    set FREQ_NAME=Každých 6 hodin
) else if "%FREQUENCY%"=="3" (
    set SCHEDULE_TYPE=WEEKLY
    set START_TIME=02:00
    set MODIFIER=/d SUN
    set FREQ_NAME=Týdně v neděli v 2:00
) else if "%FREQUENCY%"=="4" (
    set SCHEDULE_TYPE=ONSTART
    set START_TIME=
    set MODIFIER=/delay 0005:00
    set FREQ_NAME=Při spuštění systému (5min zpoždění)
) else if "%FREQUENCY%"=="5" (
    echo.
    echo Zadejte vlastní parametry:
    set /p SCHEDULE_TYPE="Typ (DAILY/WEEKLY/HOURLY/ONSTART): "
    set /p START_TIME="Čas (HH:MM, nebo prázdné pro ONSTART): "
    set /p MODIFIER="Modifikátor (např. /mo 6 pro každých 6h): "
    set FREQ_NAME=Vlastní nastavení
) else (
    echo ❌ Neplatná volba!
    pause
    exit /b 1
)

echo.
echo 📋 Shrnutí konfigurace:
echo    Název úlohy: %TASK_NAME%
echo    Skript: %SCRIPT_TYPE_NAME%
echo    Frekvence: %FREQ_NAME%
echo    Soubor: %SELECTED_SCRIPT%
if defined SCRIPT_ARGS echo    Argumenty: %SCRIPT_ARGS%
echo    Adresář: %SCRIPT_DIR%
echo.

set /p CONFIRM="Pokračovat s vytvořením úlohy? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo Zrušeno uživatelem.
    pause
    exit /b 0
)

echo.
echo 🔧 Vytváření úlohy v Task Scheduler...

REM Smazání existující úlohy (pokud existuje)
schtasks /delete /tn "%TASK_NAME%" /f >nul 2>&1

REM Sestavení příkazu schtasks
set SCHTASKS_CMD=schtasks /create /tn "%TASK_NAME%" /ru SYSTEM /rl HIGHEST /f

if "%SCRIPT_TYPE%"=="1" (
    set SCHTASKS_CMD=%SCHTASKS_CMD% /tr "\"%SELECTED_SCRIPT%\""
) else (
    set SCHTASKS_CMD=%SCHTASKS_CMD% /tr "%SELECTED_SCRIPT% %SCRIPT_ARGS%"
)

set SCHTASKS_CMD=%SCHTASKS_CMD% /sc %SCHEDULE_TYPE%

if defined START_TIME (
    set SCHTASKS_CMD=%SCHTASKS_CMD% /st %START_TIME%
)

if defined MODIFIER (
    set SCHTASKS_CMD=%SCHTASKS_CMD% %MODIFIER%
)

REM Spuštění příkazu
echo Příkaz: %SCHTASKS_CMD%
echo.
%SCHTASKS_CMD%

if %errorlevel% equ 0 (
    echo.
    echo ✅ Úloha úspěšně vytvořena!
    echo.
    echo 📊 Informace o úloze:
    schtasks /query /tn "%TASK_NAME%" /fo LIST
    
    echo.
    echo 🧪 Chcete otestovat úlohu nyní?
    set /p TEST_NOW="Spustit test? (Y/N): "
    if /i "%TEST_NOW%"=="Y" (
        echo.
        echo 🚀 Spouštím test úlohy...
        schtasks /run /tn "%TASK_NAME%"
        echo.
        echo ℹ️  Úloha byla spuštěna. Zkontrolujte Task Scheduler pro stav.
        echo    Nebo použijte: schtasks /query /tn "%TASK_NAME%"
    )
    
    echo.
    echo 📝 Užitečné příkazy:
    echo    Spuštění úlohy: schtasks /run /tn "%TASK_NAME%"
    echo    Stav úlohy:     schtasks /query /tn "%TASK_NAME%"
    echo    Smazání úlohy:  schtasks /delete /tn "%TASK_NAME%" /f
    echo    Task Scheduler: taskschd.msc
    
) else (
    echo.
    echo ❌ Chyba při vytváření úlohy!
    echo    Zkontrolujte oprávnění a parametry.
)

echo.
pause
