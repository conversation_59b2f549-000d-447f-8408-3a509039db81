# first line: 349
def _download_one(auth, url: str, username: str) -> pd.DataFrame:
    # Use stream to be able to process response.raw into parquet faster
    # response = requests.get(url, auth = auth, headers = {'Referer': username, 'User-Agent': f'lakeapi/{lakeapi.__version__}'}, stream = True)
    # return pd.read_parquet(lakeapi.response_stream.ResponseStream(response.iter_content(chunk_size=1_000_000)), engine='pyarrow')

    response = requests.get(url, auth = auth, headers = {'Referer': username, 'User-Agent': f'lakeapi/{lakeapi.__version__}'})

    if response.status_code == 404:
        contents_cache.clear()
        return pd.DataFrame()
    elif response.status_code != 200:
        print('Warning: Unexpected status code', response.status_code, 'for', url)

    try:
        return pd.read_parquet(io.BytesIO(response.content), engine='pyarrow')
    except pyarrow.lib.ArrowInvalid:
        print("No data available for =", url)
        return pd.DataFrame()
