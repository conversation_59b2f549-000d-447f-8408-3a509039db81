# Crypto Lake Downloader Configuration

# AWS Configuration
aws:
  # Pokud jsou credentials v ~/.aws/credentials, můžete nechat prázdné
  # Jinak vyplňte zde:
  access_key_id: ""
  secret_access_key: ""
  region: "eu-west-1"


# Data Configuration
data:
  # Základní složka pro ukládání dat
  base_directory: "E:\\02_Data\\Crypto Lake\"
  
  # Datum od kterého stahovat data (YYYY-MM-DD)
  start_date: "2018-01-01"
  
  # Timeframy k stahování
  timeframes:
    - "1m"   # M1
    - "5m"   # M5
    - "15m"  # M15
    - "30m"  # M30
    - "1h"   # H1
    - "4h"   # H4
    - "1d"   # D1
  
  # TOP 20 coinů/tokenů (můžete upravit podle potřeby)
  symbols:
    - "BTC-USDT"
    - "ETH-USDT"
    - "BNB-USDT"
    - "XRP-USDT"
    - "ADA-USDT"
    - "DOGE-USDT"
    - "SOL-USDT"
    - "TRX-USDT"
    - "DOT-USDT"
    - "MATIC-USDT"
    - "LTC-USDT"
    - "SHIB-USDT"
    - "AVAX-USDT"
    - "UNI-USDT"
    - "ATOM-USDT"
    - "LINK-USDT"
    - "XMR-USDT"
    - "ETC-USDT"
    - "BCH-USDT"
    - "NEAR-USDT"

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  log_file: "crypto_downloader.log"
  max_log_size_mb: 10
  backup_count: 5

# Download Configuration
download:
  # Počet paralelních stahování
  max_workers: 10
  
  # Timeout pro stahování (sekundy)
  timeout: 300
  
  # Retry počet při chybě
  max_retries: 3
  
  # Delay mezi retry (sekundy)
  retry_delay: 5
