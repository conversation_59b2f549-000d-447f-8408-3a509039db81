#!/usr/bin/env python3
"""
Crypto Lake Data Downloader - Oficiální Lake API verze
Stahuje OHLCV data z crypto-lake.com pomoc<PERSON> jejich oficiálního Python API
Podporuje paralelní stahování a inkrementální update
"""

import os
import sys
import logging
try:
    import yaml
except ImportError:
    print("❌ PyYAML není nainstalován! Spusťte: pip install PyYAML")
    sys.exit(1)
import pandas as pd
import pyarrow.parquet as pq
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from pathlib import Path
import time
from typing import List, Dict, Optional, Tuple
from tqdm import tqdm


class CryptoLakeDownloader:
    """Hlavní třída pro stahování dat z Crypto Lake pomocí oficiálního API"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """Inicializace downloaderu s konfigurací"""
        self.config = self._load_config(config_path)

        # Mapování timeframů na složky (musí být před _setup_directories)
        self.timeframe_mapping = {
            "1m": "M1",
            "5m": "M5",
            "15m": "M15",
            "30m": "M30",
            "1h": "H1",
            "4h": "H4",
            "1d": "D1"
        }

        # Mapování na Lake API timeframy (používají candles data)
        self.lake_timeframe_mapping = {
            "1m": "1m",
            "5m": "5m",
            "15m": "15m",
            "30m": "30m",
            "1h": "1h",
            "4h": "4h",
            "1d": "1d"
        }

        self._setup_logging()
        self._setup_directories()
        self._setup_lake_api()
        
        self.logger.info("Crypto Lake Downloader (Lake API) inicializován")
    
    def _load_config(self, config_path: str) -> Dict:
        """Načte konfiguraci ze YAML souboru"""
        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Konfigurační soubor {config_path} nenalezen!")
            sys.exit(1)
        except yaml.YAMLError as e:
            print(f"Chyba při načítání konfigurace: {e}")
            sys.exit(1)
    
    def _setup_logging(self):
        """Nastavení loggingu"""
        log_config = self.config['logging']
        
        # Vytvoření loggeru
        self.logger = logging.getLogger('crypto_downloader_lake')
        self.logger.setLevel(getattr(logging, log_config['level']))
        
        # Formát logů
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # File handler s rotací
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_config['log_file'],
            maxBytes=log_config['max_log_size_mb'] * 1024 * 1024,
            backupCount=log_config['backup_count']
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def _setup_directories(self):
        """Vytvoření adresářové struktury"""
        base_dir = Path(self.config['data']['base_directory'])
        
        for timeframe in self.config['data']['timeframes']:
            folder_name = self.timeframe_mapping.get(timeframe, timeframe.upper())
            folder_path = base_dir / folder_name
            folder_path.mkdir(parents=True, exist_ok=True)
            
        self.logger.info(f"Adresářová struktura vytvořena v: {base_dir}")
    
    def _setup_lake_api(self):
        """Nastavení Lake API"""
        try:
            import lakeapi
            self.lakeapi = lakeapi
            
            # Nastavení AWS credentials pro Lake API
            aws_config = self.config.get('aws', {})
            if aws_config.get('access_key_id') and aws_config.get('secret_access_key'):
                os.environ['AWS_ACCESS_KEY_ID'] = aws_config['access_key_id']
                os.environ['AWS_SECRET_ACCESS_KEY'] = aws_config['secret_access_key']
                os.environ['AWS_DEFAULT_REGION'] = aws_config.get('region', 'eu-west-1')
            
            self.logger.info("Lake API připraveno")
            
        except ImportError:
            self.logger.error("Lake API není nainstalováno! Spusťte: pip install lakeapi")
            sys.exit(1)
    
    def _get_existing_data_info(self, symbol: str, timeframe: str) -> Optional[datetime]:
        """Zjistí datum posledních dat pro daný symbol a timeframe"""
        folder_name = self.timeframe_mapping.get(timeframe, timeframe.upper())
        file_path = Path(self.config['data']['base_directory']) / folder_name / f"{symbol}-{folder_name}.parquet"
        
        if not file_path.exists():
            return None
            
        try:
            # Načti metadata z parquet souboru
            parquet_file = pq.ParquetFile(file_path)
            df = parquet_file.read().to_pandas()
            
            if len(df) > 0 and 'timestamp' in df.columns:
                # Převeď timestamp na datetime pokud je potřeba
                if df['timestamp'].dtype == 'object':
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                
                last_date = df['timestamp'].max()
                self.logger.debug(f"Poslední data pro {symbol}-{folder_name}: {last_date}")
                return last_date
                
        except Exception as e:
            self.logger.warning(f"Chyba při čtení existujících dat {file_path}: {e}")
            
        return None
    
    def _save_to_parquet(self, df: pd.DataFrame, symbol: str, timeframe: str):
        """Uloží DataFrame do parquet souboru"""
        folder_name = self.timeframe_mapping.get(timeframe, timeframe.upper())
        file_path = Path(self.config['data']['base_directory']) / folder_name / f"{symbol}-{folder_name}.parquet"
        
        try:
            # Pokud soubor existuje, načti existující data a spoj je
            if file_path.exists():
                existing_df = pd.read_parquet(file_path)
                
                # Spoj data a odstraň duplicity
                combined_df = pd.concat([existing_df, df], ignore_index=True)
                combined_df = combined_df.drop_duplicates(subset=['timestamp'], keep='last')
                combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
                
                df = combined_df
            
            # Ulož do parquet
            df.to_parquet(file_path, index=False, engine='pyarrow')
            self.logger.info(f"Data uložena: {file_path} ({len(df)} záznamů)")
            
        except Exception as e:
            self.logger.error(f"Chyba při ukládání {file_path}: {e}")
    
    def _get_date_range_for_download(self, symbol: str, timeframe: str) -> Tuple[datetime, datetime]:
        """Určí rozsah dat k stažení pro daný symbol a timeframe"""
        start_date = datetime.strptime(self.config['data']['start_date'], '%Y-%m-%d')
        end_date = datetime.now()
        
        # Zkontroluj existující data
        last_data_date = self._get_existing_data_info(symbol, timeframe)
        
        if last_data_date:
            # Inkrementální update - stahuj od posledního data
            start_date = last_data_date + timedelta(days=1)
            self.logger.info(f"Inkrementální update pro {symbol}-{timeframe} od {start_date.date()}")
        else:
            self.logger.info(f"Nové stahování pro {symbol}-{timeframe} od {start_date.date()}")
        
        return start_date, end_date

    def _download_symbol_timeframe(self, symbol: str, timeframe: str) -> bool:
        """Stáhne data pro jeden symbol a timeframe pomocí Lake API"""
        try:
            start_date, end_date = self._get_date_range_for_download(symbol, timeframe)

            # Pokud už máme aktuální data, přeskoč
            if start_date >= end_date:
                self.logger.info(f"Data pro {symbol}-{timeframe} jsou aktuální")
                return True

            # Převeď symbol na Lake API formát
            lake_symbol = symbol  # Lake API používá stejný formát

            # Převeď exchange (předpokládáme Binance)
            exchange = "BINANCE"

            # Stáhni data pomocí Lake API
            self.logger.info(f"Stahování {symbol}-{timeframe} od {start_date.date()} do {end_date.date()}")

            # Pro OHLCV data používáme "candles" tabulku
            df = self.lakeapi.load_data(
                table="candles",
                start=start_date,
                end=end_date,
                symbols=[lake_symbol],
                exchanges=[exchange],
            )

            if df is None or len(df) == 0:
                self.logger.warning(f"Žádná data nalezena pro {symbol}-{timeframe}")
                return False

            # Standardizace sloupců pro naši strukturu
            if 'origin_time' in df.columns:
                df['timestamp'] = pd.to_datetime(df['origin_time'])
            elif 'received_time' in df.columns:
                df['timestamp'] = pd.to_datetime(df['received_time'])

            # Ujisti se, že máme OHLCV sloupce
            required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

            # Zkontroluj dostupné sloupce
            available_columns = df.columns.tolist()
            self.logger.debug(f"Dostupné sloupce: {available_columns}")

            # Mapování sloupců pokud je potřeba
            column_mapping = {}
            for col in required_columns:
                if col not in df.columns:
                    # Zkus najít podobný sloupec
                    if col == 'timestamp' and 'origin_time' in df.columns:
                        column_mapping['origin_time'] = 'timestamp'
                    elif col == 'timestamp' and 'received_time' in df.columns:
                        column_mapping['received_time'] = 'timestamp'

            # Aplikuj mapování
            if column_mapping:
                df = df.rename(columns=column_mapping)

            # Zkontroluj, že máme všechny potřebné sloupce
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"Chybí sloupce pro {symbol}-{timeframe}: {missing_columns}")
                return False

            # Vyber pouze potřebné sloupce
            df = df[required_columns].copy()

            # Ujisti se, že timestamp je datetime
            df['timestamp'] = pd.to_datetime(df['timestamp'])

            # Seřaď podle času
            df = df.sort_values('timestamp').reset_index(drop=True)

            # Odstraň duplicity
            df = df.drop_duplicates(subset=['timestamp'], keep='last')

            if len(df) > 0:
                self._save_to_parquet(df, symbol, timeframe)
                self.logger.info(f"Úspěšně staženo {len(df)} záznamů pro {symbol}-{timeframe}")
                return True
            else:
                self.logger.warning(f"Žádná data po zpracování pro {symbol}-{timeframe}")
                return False

        except Exception as e:
            self.logger.error(f"Chyba při stahování {symbol}-{timeframe}: {e}")
            return False

    def download_all_data(self):
        """Hlavní metoda pro stahování všech dat"""
        symbols = self.config['data']['symbols']
        timeframes = self.config['data']['timeframes']
        max_workers = self.config['download']['max_workers']

        # Vytvoř seznam všech úkolů
        tasks = []
        for symbol in symbols:
            for timeframe in timeframes:
                tasks.append((symbol, timeframe))

        self.logger.info(f"Spouštím stahování {len(tasks)} úkolů s {max_workers} vlákny")

        # Paralelní stahování
        successful_downloads = 0
        failed_downloads = 0

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Spusť všechny úkoly
            future_to_task = {
                executor.submit(self._download_symbol_timeframe, symbol, timeframe): (symbol, timeframe)
                for symbol, timeframe in tasks
            }

            # Zpracuj výsledky
            for future in tqdm(as_completed(future_to_task), total=len(tasks), desc="Celkový průběh"):
                symbol, timeframe = future_to_task[future]
                try:
                    success = future.result()
                    if success:
                        successful_downloads += 1
                    else:
                        failed_downloads += 1

                except Exception as e:
                    self.logger.error(f"Neočekávaná chyba pro {symbol}-{timeframe}: {e}")
                    failed_downloads += 1

        # Shrnutí
        self.logger.info(f"Stahování dokončeno!")
        self.logger.info(f"Úspěšné: {successful_downloads}")
        self.logger.info(f"Neúspěšné: {failed_downloads}")
        self.logger.info(f"Celkem: {len(tasks)}")

        return successful_downloads, failed_downloads

    def get_download_statistics(self):
        """Vrátí statistiky stažených dat"""
        stats = {}
        base_dir = Path(self.config['data']['base_directory'])

        for timeframe in self.config['data']['timeframes']:
            folder_name = self.timeframe_mapping.get(timeframe, timeframe.upper())
            folder_path = base_dir / folder_name

            if folder_path.exists():
                files = list(folder_path.glob("*.parquet"))
                stats[folder_name] = {
                    'files_count': len(files),
                    'files': []
                }

                for file_path in files:
                    try:
                        df = pd.read_parquet(file_path)
                        file_stats = {
                            'name': file_path.name,
                            'size_mb': file_path.stat().st_size / (1024 * 1024),
                            'records': len(df),
                            'date_range': {
                                'start': df['timestamp'].min().isoformat() if len(df) > 0 else None,
                                'end': df['timestamp'].max().isoformat() if len(df) > 0 else None
                            }
                        }
                        stats[folder_name]['files'].append(file_stats)
                    except Exception as e:
                        self.logger.warning(f"Chyba při čtení statistik {file_path}: {e}")

        return stats


def main():
    """Hlavní funkce"""
    print("🚀 Crypto Lake Downloader (Lake API)")
    print("=" * 50)

    try:
        # Inicializace downloaderu
        downloader = CryptoLakeDownloader()

        # Zobraz konfiguraci
        print(f"📁 Ukládání do: {downloader.config['data']['base_directory']}")
        print(f"📅 Od data: {downloader.config['data']['start_date']}")
        print(f"💰 Symboly: {len(downloader.config['data']['symbols'])}")
        print(f"⏰ Timeframy: {', '.join(downloader.config['data']['timeframes'])}")
        print(f"🔄 Paralelní vlákna: {downloader.config['download']['max_workers']}")
        print()

        # Spusť stahování
        start_time = time.time()
        successful, failed = downloader.download_all_data()
        end_time = time.time()

        # Zobraz výsledky
        print()
        print("📊 Výsledky:")
        print(f"✅ Úspěšné: {successful}")
        print(f"❌ Neúspěšné: {failed}")
        print(f"⏱️  Čas: {end_time - start_time:.2f} sekund")

        # Zobraz statistiky
        print()
        print("📈 Statistiky souborů:")
        stats = downloader.get_download_statistics()

        for timeframe, data in stats.items():
            print(f"\n{timeframe}:")
            print(f"  Souborů: {data['files_count']}")

            total_records = sum(f['records'] for f in data['files'])
            total_size = sum(f['size_mb'] for f in data['files'])

            print(f"  Celkem záznamů: {total_records:,}")
            print(f"  Celková velikost: {total_size:.2f} MB")

            if data['files']:
                print("  Soubory:")
                for file_info in data['files'][:5]:  # Zobraz prvních 5
                    print(f"    {file_info['name']}: {file_info['records']:,} záznamů")

                if len(data['files']) > 5:
                    print(f"    ... a {len(data['files']) - 5} dalších")

    except KeyboardInterrupt:
        print("\n⏹️  Stahování přerušeno uživatelem")
    except Exception as e:
        print(f"\n❌ Kritická chyba: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
