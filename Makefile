# Crypto Lake Downloader Makefile

.PHONY: help install setup download test clean stats

# Výchozí cíl
help:
	@echo "🚀 Crypto Lake Downloader"
	@echo "========================="
	@echo ""
	@echo "Dostupné příkazy:"
	@echo "  make install    - Instalace závislostí"
	@echo "  make setup      - Nastavení AWS credentials"
	@echo "  make download   - Spuštění stahování"
	@echo "  make test       - Test stažených dat"
	@echo "  make stats      - Zobrazení statistik"
	@echo "  make clean      - Vyčištění logů a cache"
	@echo "  make schedule   - Nastavení automatického spouštění"
	@echo "  make help       - Zobrazení této nápovědy"

# Instalace závislostí
install:
	@echo "📦 Instalace závislostí..."
	pip3 install -r requirements.txt
	@echo "✅ Závislosti nainstalovány!"

# Nastavení AWS
setup:
	@echo "🔧 Nastavení AWS credentials..."
	python3 setup_aws.py

# Stahování dat
download:
	@echo "⬇️  Spouštím stahování dat..."
	python3 crypto_downloader.py

# Test dat
test:
	@echo "🧪 Testování stažených dat..."
	python3 test_data.py

# Statistiky
stats:
	@echo "📊 Generování statistik..."
	@python3 -c "from crypto_downloader import CryptoLakeDownloader; d = CryptoLakeDownloader(); stats = d.get_download_statistics(); [print(f'{k}: {v[\"files_count\"]} souborů') for k, v in stats.items()]"

# Vyčištění
clean:
	@echo "🧹 Vyčišťování..."
	rm -f *.log
	rm -f *.png
	find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
	find . -name "*.pyc" -delete 2>/dev/null || true
	@echo "✅ Vyčištěno!"

# Rychlé spuštění (instalace + setup + download)
quickstart: install setup download
	@echo "🎉 Rychlé spuštění dokončeno!"

# Aktualizace dat (pouze stahování)
update:
	@echo "🔄 Aktualizace dat..."
	python crypto_downloader.py

# Zobrazení struktury dat
tree:
	@echo "📁 Struktura dat:"
	@if command -v tree >/dev/null 2>&1; then \
		tree crypto_data/ -L 2; \
	else \
		find crypto_data/ -type d | head -20; \
	fi

# Kontrola konfigurace
check-config:
	@echo "⚙️  Kontrola konfigurace..."
	@python -c "import yaml; config = yaml.safe_load(open('config.yaml')); print(f'✅ Symboly: {len(config[\"data\"][\"symbols\"])}'); print(f'✅ Timeframy: {len(config[\"data\"][\"timeframes\"])}'); print(f'✅ Složka: {config[\"data\"][\"base_directory\"]}')"

# Nastavení automatického spouštění
schedule:
	@echo "🕒 Nastavení automatického spouštění..."
	@if [ "$(shell uname)" = "Darwin" ] || [ "$(shell uname)" = "Linux" ]; then \
		echo "🐧 Detekován Unix systém - spouštím setup_cron.sh"; \
		./setup_cron.sh; \
	else \
		echo "🪟 Pro Windows spusťte: setup_scheduler.bat"; \
		echo "   Nebo použijte PowerShell: setup_scheduler.ps1"; \
	fi

# Zobrazení posledních logů
logs:
	@echo "📝 Poslední logy:"
	@if [ -f crypto_downloader.log ]; then \
		tail -20 crypto_downloader.log; \
	else \
		echo "Žádné logy nenalezeny"; \
	fi
