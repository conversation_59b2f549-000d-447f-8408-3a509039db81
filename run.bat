@echo off
REM Crypto Lake Downloader - Windows Batch Script
REM Autor: Crypto Lake Downloader
REM Verze: 1.0

echo 🚀 Crypto Lake Downloader - Windows spuštění
echo ================================================

REM Nastavení kódování pro české znaky
chcp 65001 >nul

REM Získání aktuálního ad<PERSON>
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

REM Kontrola Python instalace
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python není nainstalován nebo není v PATH!
    echo    Stáhněte Python z https://python.org
    pause
    exit /b 1
)

REM Zobrazení Python verze
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 🐍 Python verze: %PYTHON_VERSION%

REM Kontrola závislostí
echo 📦 Kontrola závislostí...
python -c "import lakeapi, pandas, pyarrow, yaml, tqdm" 2>nul
if errorlevel 1 (
    echo ⚠️  Některé závislosti chybí. Instaluji...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Chyba při instalaci závislostí!
        pause
        exit /b 1
    )
)

REM Kontrola AWS credentials
echo 🔐 Kontrola AWS credentials...
if not exist "%USERPROFILE%\.aws\credentials" (
    echo ⚠️  AWS credentials nenalezeny!
    echo    Spusťte: python setup_aws.py
    pause
    exit /b 1
)

REM Kontrola konfigurace
echo ⚙️  Kontrola konfigurace...
if not exist "config.yaml" (
    echo ❌ Konfigurační soubor config.yaml nenalezen!
    pause
    exit /b 1
)

REM Vytvoření složek pro logy a zálohy
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups

REM Vytvoření backup složky s časovým razítkem
for /f "tokens=1-6 delims=/:. " %%a in ("%date% %time%") do (
    set TIMESTAMP=%%c%%b%%a_%%d%%e%%f
)
set TIMESTAMP=%TIMESTAMP: =0%
set BACKUP_DIR=backups\%TIMESTAMP%
mkdir "%BACKUP_DIR%" 2>nul

REM Backup existujících dat (pokud existují)
if exist "crypto_data" (
    echo 💾 Zálohuji existující data do %BACKUP_DIR%...
    xcopy "crypto_data" "%BACKUP_DIR%\crypto_data\" /E /I /Q >nul 2>&1
)

REM Spuštění downloaderu
echo ⬇️  Spouštím stahování dat...
echo    Čas spuštění: %date% %time%

REM Vytvoření log souboru s časovým razítkem
set LOG_FILE=logs\crypto_downloader_%TIMESTAMP%.log

REM Spuštění s logováním
echo Spouštím Crypto Lake Downloader... > "%LOG_FILE%"
echo Čas spuštění: %date% %time% >> "%LOG_FILE%"
echo. >> "%LOG_FILE%"

python crypto_downloader.py 2>&1 | tee "%LOG_FILE%"
set EXIT_CODE=%ERRORLEVEL%

echo.
echo 📊 Shrnutí spuštění:
echo    Čas dokončení: %date% %time%
echo    Exit kód: %EXIT_CODE%
echo    Log soubor: %LOG_FILE%

if %EXIT_CODE% equ 0 (
    echo ✅ Stahování dokončeno úspěšně!
    
    REM Zobrazení statistik
    echo.
    echo 📈 Rychlé statistiky:
    if exist "crypto_data" (
        for /f %%i in ('dir "crypto_data" /AD /B 2^>nul ^| find /c /v ""') do echo    Složky: %%i
        for /f %%i in ('dir "crypto_data\*.parquet" /S /B 2^>nul ^| find /c /v ""') do echo    Soubory: %%i
        for /f "tokens=3" %%i in ('dir "crypto_data" /S /-C 2^>nul ^| find "soubor"') do echo    Velikost: %%i bytes
    )
) else (
    echo ❌ Stahování selhalo s kódem %EXIT_CODE%
    echo    Zkontrolujte log: %LOG_FILE%
)

REM Pokud je spuštěno interaktivně, počkej na stisk klávesy
echo %CMDCMDLINE% | find /i "%~0" >nul
if not errorlevel 1 (
    echo.
    echo Stiskněte libovolnou klávesu pro ukončení...
    pause >nul
)

exit /b %EXIT_CODE%
