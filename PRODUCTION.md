# Crypto Lake Downloader - Produk<PERSON>ní nasazení

## 🚀 Rych<PERSON> nasazení

### 1. Příprava prostředí

```bash
# Klonování nebo stažení projektu
git clone <repository-url>
cd Crypto-lake_downloader

# Instalace závislostí
make install
# nebo
pip3 install -r requirements.txt
```

### 2. Konfigurace AWS

```bash
# Nastavení AWS credentials
make setup
# nebo
python3 setup_aws.py
```

**Zadejte vaše AWS credentials z crypto-lake.com:**
- AWS Access Key ID
- AWS Secret Access Key

### 3. Konfigurace aplikace

Upravte `config.yaml` podle potřeby:

```yaml
data:
  base_directory: "./crypto_data"  # Cesta k datům
  start_date: "2018-01-01"         # Datum od kterého stahovat
  symbols:                         # Symboly k stahování
    - "BTC-USDT"
    - "ETH-USDT"
    # ... další symboly
  timeframes:                      # Timeframy
    - "1m"   # M1
    - "5m"   # M5
    - "15m"  # M15
    - "30m"  # M30
    - "1h"   # H1
    - "4h"   # H4
    - "1d"   # D1

download:
  max_workers: 4                   # Počet paralelních vláken
  timeout: 300                     # Timeout v sekundách
  max_retries: 3                   # Počet opakování při chybě
```

### 4. Spuštění

#### Jednoduché spuštění:
```bash
make download
# nebo
python3 crypto_downloader.py
```

#### Produkční spuštění s logováním:
```bash
./run.sh
```

#### Spuštění na pozadí:
```bash
nohup ./run.sh > crypto_downloader.out 2>&1 &
```

## 📊 Monitoring a údržba

### Kontrola stavu
```bash
# Zobrazení statistik
make stats

# Kontrola logů
tail -f crypto_downloader.log

# Kontrola struktury dat
make tree
```

### Pravidelné spouštění (cron)

Přidejte do crontab pro denní spuštění:

```bash
# Editace crontab
crontab -e

# Přidejte řádek pro denní spuštění v 2:00
0 2 * * * cd /path/to/Crypto-lake_downloader && ./run.sh >> logs/cron.log 2>&1
```

### Monitoring velikosti dat

```bash
# Kontrola velikosti dat
du -sh crypto_data/

# Kontrola počtu souborů
find crypto_data -name "*.parquet" | wc -l

# Kontrola nejnovějších dat
find crypto_data -name "*.parquet" -exec ls -la {} \; | head -10
```

## 🔧 Optimalizace pro produkci

### 1. Výkon

- **Paralelní vlákna**: Nastavte `max_workers` podle CPU (doporučeno: 4-8)
- **Síťové připojení**: Stabilní připojení s dostatečnou rychlostí
- **Disk space**: Minimálně 100GB pro kompletní historická data

### 2. Bezpečnost

- **AWS credentials**: Nikdy necommitujte do git
- **Oprávnění souborů**: `chmod 600 ~/.aws/credentials`
- **Firewall**: Povolte pouze potřebné porty

### 3. Zálohy

```bash
# Automatické zálohy dat
rsync -av crypto_data/ backup_location/

# Komprese starších dat
tar -czf crypto_data_$(date +%Y%m%d).tar.gz crypto_data/
```

## 🚨 Řešení problémů

### Časté problémy

1. **AWS credentials chyby**
   ```bash
   # Znovu nastavte credentials
   python3 setup_aws.py
   ```

2. **Nedostatek místa na disku**
   ```bash
   # Kontrola místa
   df -h
   
   # Vyčištění starých logů
   make clean
   ```

3. **Síťové chyby**
   - Zkontrolujte internetové připojení
   - Zvyšte `timeout` v config.yaml
   - Zvyšte `max_retries`

4. **Python závislosti**
   ```bash
   # Reinstalace závislostí
   pip3 install --upgrade -r requirements.txt
   ```

### Logy a debugging

```bash
# Detailní logy
# V config.yaml nastavte: level: "DEBUG"

# Kontrola chyb
grep "ERROR" crypto_downloader.log

# Kontrola varování
grep "WARNING" crypto_downloader.log
```

## 📈 Škálování

### Horizontální škálování

1. **Rozdělení symbolů**: Spusťte více instancí s různými symboly
2. **Rozdělení timeframů**: Různé instance pro různé timeframy
3. **Load balancing**: Použijte load balancer pro distribuci zátěže

### Vertikální škálování

1. **Více CPU**: Zvyšte `max_workers`
2. **Více RAM**: Pro větší datasety
3. **Rychlejší disk**: SSD pro lepší I/O výkon

## 🔄 Aktualizace

```bash
# Backup před aktualizací
cp -r crypto_data crypto_data_backup

# Stažení nové verze
git pull origin main

# Aktualizace závislostí
pip3 install --upgrade -r requirements.txt

# Test nové verze
python3 crypto_downloader.py --dry-run  # pokud je implementováno
```

## 📞 Podpora

Pro problémy nebo dotazy:

1. Zkontrolujte logy v `crypto_downloader.log`
2. Ověřte konfiguraci v `config.yaml`
3. Spusťte `python3 setup_aws.py` pro test připojení
4. Zkontrolujte dokumentaci crypto-lake.com

## 📋 Checklist pro produkci

- [ ] AWS credentials nastaveny a otestovány
- [ ] Konfigurace upravena podle potřeb
- [ ] Dostatečné místo na disku
- [ ] Stabilní internetové připojení
- [ ] Monitoring nastaven
- [ ] Zálohy nakonfigurovány
- [ ] Cron job nastaven (pokud potřeba)
- [ ] Logy rotovány
- [ ] Dokumentace přečtena
